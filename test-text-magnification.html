<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Magnification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .small-text {
            font-size: 12px;
            color: #666;
        }
        .medium-text {
            font-size: 14px;
            color: #333;
        }
        .large-text {
            font-size: 18px;
            color: #000;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4f46e5;
        }
        .toggle-button {
            background: #10b981;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .toggle-button.active {
            background: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Text Magnification Feature Test</h1>
        
        <button id="toggleMagnification" class="toggle-button">
            🔍 Enable Text Magnification
        </button>
        
        <div class="test-section">
            <h2>Small Text Test</h2>
            <p class="small-text">This is small text (12px) that should be magnified when you hover over it. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>
        
        <div class="test-section">
            <h2>Medium Text Test</h2>
            <p class="medium-text">This is medium text (14px) for testing the magnification feature. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </div>
        
        <div class="test-section">
            <h2>Large Text Test</h2>
            <p class="large-text">This is large text (18px) to test how magnification works with different font sizes. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>
        
        <div class="test-section">
            <h2>Interactive Elements</h2>
            <button>Hover over this button</button>
            <a href="#">This is a link to test</a>
            <label>Form label text</label>
        </div>
        
        <div class="test-section">
            <h2>List Items</h2>
            <ul>
                <li>First list item with some text</li>
                <li>Second list item for testing</li>
                <li>Third list item with more content</li>
            </ul>
        </div>
    </div>

    <script>
        // Simple test implementation
        let magnificationActive = false;
        const toggleButton = document.getElementById('toggleMagnification');
        
        toggleButton.addEventListener('click', function() {
            magnificationActive = !magnificationActive;
            
            if (magnificationActive) {
                this.textContent = '🔍 Disable Text Magnification';
                this.classList.add('active');
                enableMagnification();
            } else {
                this.textContent = '🔍 Enable Text Magnification';
                this.classList.remove('active');
                disableMagnification();
            }
        });
        
        function enableMagnification() {
            console.log('Text magnification enabled');
            // This would integrate with the actual plugin functionality
        }
        
        function disableMagnification() {
            console.log('Text magnification disabled');
            // This would integrate with the actual plugin functionality
        }
    </script>
</body>
</html>
